import { SubmitHandler } from 'react-hook-form';

// Enum cho trạng thái key result
export enum KeyResultStatus {
  ACTIVE = 'ACTIVE',
  COMPLETED = 'COMPLETED',
  PAUSED = 'PAUSED',
  CANCELED = 'CANCELED',
}

// Enum cho tần suất check-in
export enum CheckInFrequency {
  DAILY = 'DAILY',
  WEEKLY = 'WEEKLY',
  MONTHLY = 'MONTHLY',
}

// Interface cho form thêm key result
export interface KeyResultFormData {
  objectiveId: number;
  title: string;
  description?: string;
  targetValue: number;
  startValue?: number;
  unit?: string;
  format?: string;
  measurementMethod?: string;
  weight?: number;
  checkInFrequency?: CheckInFrequency;
}

// Type cho hàm xử lý submit form
export type KeyResultSubmitHandler = SubmitHandler<KeyResultFormData>;
