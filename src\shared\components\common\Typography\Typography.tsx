import React, { forwardRef, HTMLAttributes, ReactNode } from 'react';
import { useResponsiveValue } from '@/shared/hooks/common';
import { Breakpoint } from '@/shared/constants/breakpoints';

// Typography variants
export type TypographyVariant =
  | 'h1'
  | 'h2'
  | 'h3'
  | 'h4'
  | 'h5'
  | 'h6'
  | 'body1'
  | 'body2'
  | 'subtitle1'
  | 'subtitle2'
  | 'caption'
  | 'overline'
  | 'code';

// Typography alignment
export type TypographyAlign = 'left' | 'center' | 'right' | 'justify';

// Typography weight
export type TypographyWeight =
  | 'thin'
  | 'extralight'
  | 'light'
  | 'normal'
  | 'medium'
  | 'semibold'
  | 'bold'
  | 'extrabold'
  | 'black';

// Typography color
export type TypographyColor =
  | 'default'
  | 'primary'
  | 'secondary'
  | 'success'
  | 'warning'
  | 'danger'
  | 'info'
  | 'light'
  | 'dark'
  | 'muted'
  | 'error';

// Typography transform
export type TypographyTransform = 'none' | 'uppercase' | 'lowercase' | 'capitalize';

// Typography responsive size
export type TypographyResponsiveSize = {
  [key in Breakpoint]?: string;
} & {
  base: string;
};

export interface TypographyProps extends Omit<HTMLAttributes<HTMLElement>, 'color'> {
  /**
   * Nội dung của Typography
   */
  children: ReactNode;

  /**
   * Variant của Typography
   * @default 'body1'
   */
  variant?: TypographyVariant;

  /**
   * Căn chỉnh văn bản
   * @default 'left'
   */
  align?: TypographyAlign;

  /**
   * Độ đậm của văn bản
   */
  weight?: TypographyWeight;

  /**
   * Màu sắc của văn bản
   * @default 'default'
   */
  color?: TypographyColor;

  /**
   * Biến đổi văn bản
   * @default 'none'
   */
  transform?: TypographyTransform;

  /**
   * Kích thước font chữ tùy chỉnh
   */
  fontSize?: string | TypographyResponsiveSize;

  /**
   * Khoảng cách giữa các dòng
   */
  lineHeight?: string;

  /**
   * Khoảng cách giữa các ký tự
   */
  letterSpacing?: string;

  /**
   * Có gạch chân không
   * @default false
   */
  underline?: boolean;

  /**
   * Có gạch ngang không
   * @default false
   */
  strikethrough?: boolean;

  /**
   * Có in nghiêng không
   * @default false
   */
  italic?: boolean;

  /**
   * Có truncate text không
   * @default false
   */
  truncate?: boolean;

  /**
   * Số dòng tối đa hiển thị (kết hợp với truncate)
   */
  lines?: number;

  /**
   * Có hiển thị dưới dạng block không
   * @default true
   */
  block?: boolean;

  /**
   * Có tự động wrap không
   * @default true
   */
  noWrap?: boolean;

  /**
   * Có ghi đè component mặc định không
   */
  component?: React.ElementType;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Typography component hiển thị văn bản với nhiều kiểu dáng khác nhau
 *
 * @example
 * // Heading
 * <Typography variant="h1">Heading 1</Typography>
 *
 * @example
 * // Body text
 * <Typography variant="body1">Regular paragraph text</Typography>
 *
 * @example
 * // Styled text
 * <Typography
 *   variant="subtitle1"
 *   color="primary"
 *   weight="bold"
 *   align="center"
 * >
 *   Styled text
 * </Typography>
 */
const Typography = forwardRef<HTMLElement, TypographyProps>(
  (
    {
      children,
      variant = 'body1',
      align = 'left',
      weight,
      color = 'default',
      transform = 'none',
      fontSize,
      lineHeight,
      letterSpacing,
      underline = false,
      strikethrough = false,
      italic = false,
      truncate = false,
      lines,
      block = true,
      noWrap = false,
      component,
      className = '',
      ...rest
    },
    ref
  ) => {
    // Map variant to HTML element
    const variantMapping: Record<TypographyVariant, React.ElementType> = {
      h1: 'h1',
      h2: 'h2',
      h3: 'h3',
      h4: 'h4',
      h5: 'h5',
      h6: 'h6',
      body1: 'p',
      body2: 'p',
      subtitle1: 'h6',
      subtitle2: 'h6',
      caption: 'span',
      overline: 'span',
      code: 'code',
    };

    // Map variant to Tailwind classes
    const variantClasses: Record<TypographyVariant, string> = {
      h1: 'text-4xl sm:text-5xl font-bold',
      h2: 'text-3xl sm:text-4xl font-bold',
      h3: 'text-2xl sm:text-3xl font-bold',
      h4: 'text-xl sm:text-2xl font-semibold',
      h5: 'text-lg sm:text-xl font-semibold',
      h6: 'text-base sm:text-lg font-semibold',
      body1: 'text-base',
      body2: 'text-sm',
      subtitle1: 'text-lg font-medium',
      subtitle2: 'text-base font-medium',
      caption: 'text-xs',
      overline: 'text-xs uppercase tracking-wider',
      code: 'font-mono text-sm bg-gray-100 dark:bg-dark-light px-1 py-0.5 rounded',
    };

    // Map alignment to Tailwind classes
    const alignClasses: Record<TypographyAlign, string> = {
      left: 'text-left',
      center: 'text-center',
      right: 'text-right',
      justify: 'text-justify',
    };

    // Map weight to Tailwind classes
    const weightClasses: Record<TypographyWeight, string> = {
      thin: 'font-thin',
      extralight: 'font-extralight',
      light: 'font-light',
      normal: 'font-normal',
      medium: 'font-medium',
      semibold: 'font-semibold',
      bold: 'font-bold',
      extrabold: 'font-extrabold',
      black: 'font-black',
    };

    // Map color to Tailwind classes
    const colorClasses: Record<TypographyColor, string> = {
      default: '',
      primary: 'text-primary',
      secondary: 'text-secondary',
      success: 'text-green-500',
      warning: 'text-yellow-500',
      danger: 'text-red-500',
      info: 'text-blue-500',
      light: 'text-gray-300',
      dark: 'text-gray-800 dark:text-gray-200',
      muted: 'text-gray-500 dark:text-gray-400',
      error: 'text-red-600',
    };

    // Map transform to Tailwind classes
    const transformClasses: Record<TypographyTransform, string> = {
      none: '',
      uppercase: 'uppercase',
      lowercase: 'lowercase',
      capitalize: 'capitalize',
    };

    // Luôn gọi hook không phụ thuộc vào điều kiện
    // Sử dụng hook ở cấp cao nhất của component
    const responsiveSize = useResponsiveValue(
      typeof fontSize === 'object' ? fontSize : { base: '' }
    );

    // Handle responsive font size
    let fontSizeClass = '';
    if (fontSize) {
      if (typeof fontSize === 'string') {
        fontSizeClass = `text-[${fontSize}]`;
      } else if (responsiveSize) {
        // Use the responsive value hook for responsive font sizes
        fontSizeClass = `text-[${responsiveSize}]`;
      }
    }

    // Combine all classes
    const classes = [
      variantClasses[variant],
      alignClasses[align],
      weight ? weightClasses[weight] : '',
      colorClasses[color],
      transformClasses[transform],
      fontSizeClass,
      lineHeight ? `leading-[${lineHeight}]` : '',
      letterSpacing ? `tracking-[${letterSpacing}]` : '',
      underline ? 'underline' : '',
      strikethrough ? 'line-through' : '',
      italic ? 'italic' : '',
      truncate ? 'truncate' : '',
      lines && truncate ? `line-clamp-${lines}` : '',
      block ? 'block' : 'inline',
      noWrap ? 'whitespace-nowrap' : '',
      className,
    ]
      .filter(Boolean)
      .join(' ');

    // Determine the component to render
    const Component = component || variantMapping[variant];

    return (
      <Component ref={ref} className={classes} {...rest}>
        {children}
      </Component>
    );
  }
);

Typography.displayName = 'Typography';

export default Typography;
