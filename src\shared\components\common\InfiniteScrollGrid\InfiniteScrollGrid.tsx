import React, { ReactNode } from 'react';
import { useTranslation } from 'react-i18next';

import { ResponsiveGrid } from '@/shared/components/common';

export interface InfiniteScrollGridProps<T = any> {
  /**
   * Dữ liệu để hiển thị
   */
  data: T[];

  /**
   * Function render cho mỗi item
   */
  renderItem: (item: T, index: number) => ReactNode;

  /**
   * Key function để tạo unique key cho mỗi item
   */
  keyExtractor: (item: T, index: number) => string | number;

  /**
   * Metadata phân trang
   */
  meta?: {
    currentPage: number;
    totalPages: number;
    totalItems?: number;
  };

  /**
   * Trạng thái loading
   */
  isLoading?: boolean;

  /**
   * Ref cho load more trigger
   */
  loadMoreRef?: React.RefObject<HTMLDivElement>;

  /**
   * Cấu hình responsive grid
   */
  maxColumns?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };

  /**
   * Cấu hình responsive grid khi có chat panel
   */
  maxColumnsWithChatPanel?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };

  /**
   * Gap giữa các items
   */
  gap?: number | string;

  /**
   * Class name bổ sung
   */
  className?: string;

  /**
   * Text hiển thị khi loading
   */
  loadingText?: string;

  /**
   * Text hiển thị khi có thể load more
   */
  loadMoreText?: string;

  /**
   * Ẩn load more indicator
   */
  hideLoadMoreIndicator?: boolean;

  /**
   * Custom loading component
   */
  loadingComponent?: ReactNode;

  /**
   * Custom load more component
   */
  loadMoreComponent?: ReactNode;
}

/**
 * Component hiển thị grid với infinite scroll
 */
const InfiniteScrollGrid = <T,>({
  data,
  renderItem,
  keyExtractor,
  meta,
  isLoading = false,
  loadMoreRef,
  maxColumns = { xs: 1, sm: 2, md: 2, lg: 3, xl: 4 },
  maxColumnsWithChatPanel,
  gap,
  className = '',
  loadingText,
  loadMoreText,
  hideLoadMoreIndicator = false,
  loadingComponent,
  loadMoreComponent,
}: InfiniteScrollGridProps<T>) => {
  const { t } = useTranslation(['common']);

  // Default texts
  const defaultLoadingText = loadingText || t('common:loading', 'Đang tải...');
  const defaultLoadMoreText = loadMoreText || t('common:loadMore', 'Cuộn để tải thêm');

  // Default loading component
  const defaultLoadingComponent = loadingComponent || (
    <div className="flex items-center space-x-2">
      <div className="animate-spin rounded-full h-6 w-6 border-2 border-primary border-t-transparent"></div>
      <span className="text-gray-500">{defaultLoadingText}</span>
    </div>
  );

  // Default load more component
  const defaultLoadMoreComponent = loadMoreComponent || (
    <span className="text-gray-500">{defaultLoadMoreText}</span>
  );

  // Kiểm tra có thể load more không
  const canLoadMore = meta && meta.currentPage < meta.totalPages;

  return (
    <div className={className}>
      <ResponsiveGrid
        maxColumns={maxColumns}
        maxColumnsWithChatPanel={maxColumnsWithChatPanel}
        gap={gap}
      >
        {data.map((item, index) => (
          <div key={keyExtractor(item, index)}>
            {renderItem(item, index)}
          </div>
        ))}
      </ResponsiveGrid>

      {/* Load more indicator cho infinite scroll */}
      {!hideLoadMoreIndicator && canLoadMore && (
        <div ref={loadMoreRef} className="flex justify-center py-4">
          {isLoading ? defaultLoadingComponent : defaultLoadMoreComponent}
        </div>
      )}
    </div>
  );
};

export default InfiniteScrollGrid;
