import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { <PERSON><PERSON>, Card } from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';

import ObjectiveCreationFlow from '../components/ObjectiveCreationFlow';

/**
 * Demo page để test quy trình tạo mục tiêu 2 bước
 */
const ObjectiveCreationDemo: React.FC = () => {
  const { t } = useTranslation(['okrs', 'common']);
  const { isVisible, showForm, hideForm } = useSlideForm();

  // Xử lý khi tạo objective thành công
  const handleSuccess = () => {
    hideForm();
    alert('Tạo mục tiêu thành công!');
  };

  // Xử lý khi hủy form
  const handleCancel = () => {
    hideForm();
  };

  return (
    <div className="w-full bg-background text-foreground p-6">
      <Card className="max-w-4xl mx-auto">
        <div className="text-center space-y-4">
          <h1 className="text-2xl font-bold">
            {t('okrs:objective.demo.title', 'Demo Quy Trình Tạo Mục Tiêu 2 Bước')}
          </h1>
          <p className="text-muted-foreground">
            {t(
              'okrs:objective.demo.description',
              'Thử nghiệm quy trình tạo mục tiêu mới với 2 bước: chọn loại mục tiêu và điền form tương ứng'
            )}
          </p>
          
          <div className="pt-4">
            <Button onClick={showForm} variant="primary" size="lg">
              {t('okrs:objective.demo.startButton', 'Bắt đầu tạo mục tiêu')}
            </Button>
          </div>
        </div>

        <div className="mt-8 space-y-4">
          <h2 className="text-lg font-semibold">
            {t('okrs:objective.demo.features', 'Tính năng')}
          </h2>
          <ul className="space-y-2 text-sm text-muted-foreground">
            <li>• {t('okrs:objective.demo.feature1', 'Chọn loại mục tiêu: Cá nhân, Phòng ban, hoặc Công ty')}</li>
            <li>• {t('okrs:objective.demo.feature2', 'Form động dựa trên loại mục tiêu đã chọn')}</li>
            <li>• {t('okrs:objective.demo.feature3', 'Tích hợp AsyncSelectWithPagination cho phòng ban và nhân viên')}</li>
            <li>• {t('okrs:objective.demo.feature4', 'Validation phù hợp với từng loại mục tiêu')}</li>
            <li>• {t('okrs:objective.demo.feature5', 'Giao diện thân thiện với người dùng')}</li>
          </ul>
        </div>

        <div className="mt-8 space-y-4">
          <h2 className="text-lg font-semibold">
            {t('okrs:objective.demo.types', 'Các loại mục tiêu')}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 border border-border rounded-lg">
              <h3 className="font-medium text-blue-600">
                {t('okrs:objective.type.individual', 'Cá nhân')}
              </h3>
              <p className="text-sm text-muted-foreground mt-2">
                {t('okrs:objective.demo.individualDesc', 'Mục tiêu cho cá nhân, chỉ cần thông tin cơ bản')}
              </p>
            </div>
            <div className="p-4 border border-border rounded-lg">
              <h3 className="font-medium text-green-600">
                {t('okrs:objective.type.department', 'Phòng ban')}
              </h3>
              <p className="text-sm text-muted-foreground mt-2">
                {t('okrs:objective.demo.departmentDesc', 'Mục tiêu cho phòng ban, bao gồm chọn phòng ban và người chịu trách nhiệm')}
              </p>
            </div>
            <div className="p-4 border border-border rounded-lg">
              <h3 className="font-medium text-purple-600">
                {t('okrs:objective.type.company', 'Công ty')}
              </h3>
              <p className="text-sm text-muted-foreground mt-2">
                {t('okrs:objective.demo.companyDesc', 'Mục tiêu cho toàn công ty, chọn người chịu trách nhiệm cấp cao')}
              </p>
            </div>
          </div>
        </div>
      </Card>

      {/* Slide-in form */}
      <SlideInForm isVisible={isVisible}>
        <ObjectiveCreationFlow
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      </SlideInForm>
    </div>
  );
};

export default ObjectiveCreationDemo;
