import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { ActiveFilters } from '@/modules/components/filters';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { Card, CollapsibleCard, Table } from '@/shared/components/common';
import ActionMenu from '@/shared/components/common/ActionMenu';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { TableColumn } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { useActiveFilters } from '@/shared/hooks/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { formatDateTime } from '@/shared/utils/date';
import { formatRelativeDate } from '@/shared/utils/date-utils';

import RoleForm from '../components/forms/RoleForm';
import {
  useCreateRole,
  useDeleteRole,
  usePermissionGroups,
  useRoles,
  useUpdateRole,
} from '../hooks/usePermissions';
import {
  CreateRoleDto,
  PermissionGroupDto,
  RoleDto,
  RoleQueryDto,
  UpdateRoleDto,
} from '../types/permission.types';

/**
 * Trang quản lý phân quyền
 */
const PermissionsPage: React.FC = () => {
  const { t } = useTranslation(['hrm', 'common']);
  const [activeTab, setActiveTab] = useState<'roles' | 'permissions'>('roles');
  const [editingRole, setEditingRole] = useState<RoleDto | undefined>(undefined);

  // Sử dụng hook animation cho form
  const { isVisible, showForm, hideForm } = useSlideForm();

  // Mutations
  const createRoleMutation = useCreateRole();
  const updateRoleMutation = useUpdateRole();
  const deleteRoleMutation = useDeleteRole();

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<RoleDto>[]>(
    () => [
      { key: 'id', title: 'ID', dataIndex: 'id', width: '5%', sortable: true },
      {
        key: 'name',
        title: t('hrm:permission.table.name', 'Tên vai trò'),
        dataIndex: 'name',
        width: '20%',
        sortable: true,
      },
      {
        key: 'description',
        title: t('hrm:permission.table.description', 'Mô tả'),
        dataIndex: 'description',
        width: '30%',
        render: (value: unknown) => {
          return <div>{value ? String(value) : '-'}</div>;
        },
      },
      {
        key: 'permissions',
        title: t('hrm:permission.table.permissions', 'Số quyền'),
        dataIndex: 'permissions',
        width: '15%',
        render: (value: unknown) => {
          const permissions = value as string[];
          return <div>{permissions?.length || 0}</div>;
        },
      },
      {
        key: 'createdAt',
        title: t('hrm:permission.table.createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          if (!value) {
            return <div>-</div>;
          }

          try {
            // Kiểm tra và chuyển đổi timestamp nếu cần
            let dateValue: number;

            if (typeof value === 'string') {
              // Nếu là chuỗi, chuyển đổi sang số
              dateValue = parseInt(value, 10);

              // Nếu timestamp quá lớn (milliseconds), không cần điều chỉnh
              // Nếu timestamp là seconds, chuyển đổi sang milliseconds
              if (dateValue < 10000000000) {
                dateValue = dateValue * 1000;
              }
            } else if (typeof value === 'number') {
              dateValue = value;
              // Tương tự, chuyển đổi nếu là seconds
              if (dateValue < 10000000000) {
                dateValue = dateValue * 1000;
              }
            } else {
              return <div>-</div>;
            }

            const dateObj = new Date(dateValue);

            // Kiểm tra xem dateObj có hợp lệ không
            if (isNaN(dateObj.getTime())) {
              return <div>-</div>;
            }

            // Hiển thị cả thời gian tuyệt đối và tương đối
            return (
              <div className="flex flex-col">
                <span>{formatDateTime(dateObj)}</span>
                <span className="text-xs text-muted-foreground">{formatRelativeDate(dateObj)}</span>
              </div>
            );
          } catch (error) {
            console.error('Error rendering date:', error, value);
            return <div>-</div>;
          }
        },
      },
      {
        key: 'actions',
        title: t('common:actions', 'Thao tác'),
        width: '15%',
        render: (_: unknown, record: RoleDto) => {
          const actionItems = [
            {
              id: 'view',
              label: t('common:view', 'Xem'),
              icon: 'eye',
              onClick: () => console.log('View', record.id),
            },
            {
              id: 'edit',
              label: t('common:edit', 'Sửa'),
              icon: 'edit',
              onClick: () => handleEdit(record),
            },
            {
              id: 'delete',
              label: t('common:delete', 'Xóa'),
              icon: 'trash',
              onClick: () => handleDelete(record),
            },
          ];

          return (
            <div className="flex justify-center">
              <ActionMenu
                items={actionItems}
                menuTooltip={t('common:moreActions', 'Thêm hành động')}
                iconSize="sm"
                iconVariant="default"
                placement="bottom"
                menuWidth="180px"
                menuIcon="more-horizontal"
                showAllInMenu={true}
                preferRight={true}
                preferTop={true}
              />
            </div>
          );
        },
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): RoleQueryDto => {
    const queryParams: RoleQueryDto = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<RoleDto, RoleQueryDto>({
      columns,
      filterOptions: [],
      showDateFilter: false,
      createQueryParams,
    })
  );

  // Gọi API lấy danh sách vai trò với queryParams từ dataTable
  const { data: rolesData, isLoading } = useRoles(dataTable.queryParams);

  // Xử lý thêm mới
  const handleAdd = () => {
    setEditingRole(undefined);
    showForm();
  };

  // Xử lý chỉnh sửa
  const handleEdit = (role: RoleDto) => {
    setEditingRole(role);
    showForm();
  };

  // Xử lý submit form
  const handleSubmit = async (data: CreateRoleDto | UpdateRoleDto) => {
    try {
      if (editingRole) {
        // Cập nhật vai trò
        await updateRoleMutation.mutateAsync({
          id: editingRole.id,
          data: data as UpdateRoleDto,
        });
      } else {
        // Tạo mới vai trò
        await createRoleMutation.mutateAsync(data as CreateRoleDto);
      }
      hideForm();
      setEditingRole(undefined);
    } catch (error) {
      console.error('Error submitting role:', error);
    }
  };

  // Xử lý xóa vai trò
  const handleDelete = async (role: RoleDto) => {
    if (
      window.confirm(t('hrm:permission.confirmDelete', 'Bạn có chắc chắn muốn xóa vai trò này?'))
    ) {
      try {
        await deleteRoleMutation.mutateAsync(role.id);
      } catch (error) {
        console.error('Error deleting role:', error);
      }
    }
  };

  // Xử lý hủy form
  const handleCancel = () => {
    hideForm();
    setEditingRole(undefined);
  };

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: dataTable.tableData.handleSortChange,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {},
    t,
  });

  // Component hiển thị danh sách vai trò
  const RolesTab = () => {
    return (
      <div>
        {/* Thêm component ActiveFilters */}
        <ActiveFilters
          searchTerm={dataTable.tableData.searchTerm}
          onClearSearch={handleClearSearch}
          filterValue={dataTable.filter.selectedValue}
          filterLabel={getFilterLabel()}
          onClearFilter={handleClearFilter}
          dateRange={dataTable.dateRange.dateRange}
          onClearDateRange={handleClearDateRange}
          sortBy={dataTable.tableData.sortBy}
          sortDirection={dataTable.tableData.sortDirection}
          onClearSort={handleClearSort}
          onClearAll={handleClearAll}
        />

        <Card className="overflow-hidden">
          <Table
            columns={dataTable.columnVisibility.visibleTableColumns}
            data={rolesData?.items || []}
            rowKey="id"
            loading={isLoading}
            sortable={true}
            onSortChange={dataTable.tableData.handleSortChange}
            pagination={{
              current: rolesData?.meta.currentPage || 1,
              pageSize: dataTable.tableData.pageSize,
              total: rolesData?.meta.totalItems || 0,
              onChange: dataTable.tableData.handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>
      </div>
    );
  };

  // Component hiển thị danh sách quyền
  const PermissionsTab = () => {
    const { data: permissionGroupsData, isLoading: isLoadingPermissions } = usePermissionGroups();

    return (
      <div>
        {isLoadingPermissions ? (
          <Card className="p-4">
            <div className="flex justify-center items-center h-40">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                <p>{t('common:loading', 'Đang tải...')}</p>
              </div>
            </div>
          </Card>
        ) : (
          <div className="space-y-4">
            {permissionGroupsData?.groups.map((group: PermissionGroupDto) => {
              // Trích xuất module từ nhóm quyền hoặc từ key của group
              const groupModule = group.module || group.key.split('.')[0];

              return (
                <CollapsibleCard
                  key={group.key}
                  title={
                    <div className="flex justify-between items-center w-full">
                      <div className="flex items-center space-x-3">
                        <h3 className="text-lg font-semibold text-primary">{group.name}</h3>
                        <span className="text-xs font-medium px-2 py-1 bg-primary/10 rounded-full">
                          {groupModule}
                        </span>
                      </div>
                      <span className="text-sm text-muted-foreground">
                        {group.permissions.length} {t('hrm:permission.permissions', 'quyền')}
                      </span>
                    </div>
                  }
                  defaultOpen={false}
                  lazyLoad={true}
                  className="shadow-md"
                >
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {group.permissions.map(permission => {
                      // Trích xuất module từ quyền hoặc từ key của permission
                      const permissionModule = permission.module || permission.key.split('.')[0];

                      return (
                        <div
                          key={permission.key}
                          className="p-3 rounded-md bg-card shadow-sm hover:shadow-md transition-all duration-200 border border-border"
                        >
                          <div className="font-medium text-card-foreground mb-1">
                            {permission.name}
                          </div>
                          <div className="text-xs font-medium text-primary/80 mb-1">
                            {permissionModule}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {permission.description}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CollapsibleCard>
              );
            })}
          </div>
        )}
      </div>
    );
  };

  return (
    <div>
      <MenuIconBar
        onSearch={activeTab === 'roles' ? dataTable.tableData.handleSearch : () => {}}
        onAdd={activeTab === 'roles' ? handleAdd : undefined}
        items={activeTab === 'roles' ? dataTable.menuItems : []}
        onDateRangeChange={activeTab === 'roles' ? dataTable.dateRange.setDateRange : undefined}
        onColumnVisibilityChange={
          activeTab === 'roles' ? dataTable.columnVisibility.setVisibleColumns : undefined
        }
        columns={activeTab === 'roles' ? dataTable.columnVisibility.visibleColumns : []}
        showDateFilter={false}
        showColumnFilter={activeTab === 'roles'}
        additionalIcons={[
          {
            icon: 'users',
            tooltip: t('hrm:permission.tabs.roles', 'Vai trò'),
            onClick: () => setActiveTab('roles'),
            variant: activeTab === 'roles' ? 'primary' : 'default',
          },
          {
            icon: 'lock',
            tooltip: t('hrm:permission.tabs.permissions', 'Quyền'),
            onClick: () => setActiveTab('permissions'),
            variant: activeTab === 'permissions' ? 'primary' : 'default',
          },
        ]}
      />

      <SlideInForm isVisible={isVisible}>
        <RoleForm
          role={editingRole}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isSubmitting={createRoleMutation.isPending || updateRoleMutation.isPending}
        />
      </SlideInForm>

      {activeTab === 'roles' ? <RolesTab /> : <PermissionsTab />}
    </div>
  );
};

export default PermissionsPage;
