import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { KeyResultService } from '../services/key-result.service';
import {
  CreateKeyResultDto,
  KeyResultQueryDto,
  UpdateKeyResultDto,
} from '../types/key-result.types';

// Key cho React Query
const KEY_RESULTS_QUERY_KEY = 'keyResults';

/**
 * Hook để lấy danh sách key results
 * @param params Tham số truy vấn
 * @returns Query result với danh sách key results
 */
export const useKeyResults = (params?: KeyResultQueryDto) => {
  return useQuery({
    queryKey: [KEY_RESULTS_QUERY_KEY, params],
    queryFn: () => KeyResultService.getKeyResults(params),
    select: data => data.result,
  });
};

/**
 * Hook để lấy key results theo objective ID
 * @param objectiveId ID của objective
 * @returns Query result với danh sách key results
 */
export const useKeyResultsByObjective = (objectiveId: number) => {
  return useQuery({
    queryKey: [KEY_RESULTS_QUERY_KEY, 'objective', objectiveId],
    queryFn: () => KeyResultService.getKeyResultsByObjective(objectiveId),
    select: data => data.result,
    enabled: !!objectiveId,
  });
};

/**
 * Hook để lấy chi tiết key result
 * @param id ID key result
 * @returns Query result với chi tiết key result
 */
export const useKeyResult = (id: number) => {
  return useQuery({
    queryKey: [KEY_RESULTS_QUERY_KEY, id],
    queryFn: () => KeyResultService.getKeyResult(id),
    select: data => data.result,
    enabled: !!id,
  });
};

/**
 * Hook để tạo key result mới
 * @returns Mutation result cho việc tạo key result
 */
export const useCreateKeyResult = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateKeyResultDto) => KeyResultService.createKeyResult(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [KEY_RESULTS_QUERY_KEY] });
    },
  });
};

/**
 * Hook để cập nhật key result
 * @returns Mutation result cho việc cập nhật key result
 */
export const useUpdateKeyResult = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateKeyResultDto }) =>
      KeyResultService.updateKeyResult(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [KEY_RESULTS_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [KEY_RESULTS_QUERY_KEY, variables.id] });
    },
  });
};

/**
 * Hook để xóa key result
 * @returns Mutation result cho việc xóa key result
 */
export const useDeleteKeyResult = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => KeyResultService.deleteKeyResult(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [KEY_RESULTS_QUERY_KEY] });
    },
  });
};
