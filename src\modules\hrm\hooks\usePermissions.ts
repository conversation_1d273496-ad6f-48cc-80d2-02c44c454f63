import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { PermissionService } from '../services/permission.service';
import { CreateRoleDto, RoleQueryDto, UpdateRoleDto } from '../types/permission.types';

// Key cho React Query
const ROLES_QUERY_KEY = 'roles';
const PERMISSIONS_QUERY_KEY = 'permissions';

/**
 * Hook để lấy danh sách vai trò
 * @param params Tham số truy vấn
 * @returns Query result với danh sách vai trò
 */
export const useRoles = (params?: RoleQueryDto) => {
  return useQuery({
    queryKey: [ROLES_QUERY_KEY, params],
    queryFn: () => PermissionService.getRoles(params),
    select: data => data.result,
  });
};

/**
 * Hook để lấy chi tiết vai trò
 * @param id ID vai trò
 * @returns Query result với chi tiết vai trò
 */
export const useRole = (id: number) => {
  return useQuery({
    queryKey: [ROLES_QUERY_KEY, id],
    queryFn: () => PermissionService.getRole(id),
    select: data => data.result,
    enabled: !!id,
  });
};

/**
 * Hook để lấy danh sách quyền theo nhóm
 * @returns Query result với danh sách quyền theo nhóm
 */
export const usePermissionGroups = () => {
  return useQuery({
    queryKey: [PERMISSIONS_QUERY_KEY, 'groups'],
    queryFn: () => PermissionService.getPermissionGroups(),
    select: data => data.result,
  });
};

/**
 * Hook để lấy danh sách vai trò của nhân viên
 * @param employeeId ID nhân viên
 * @returns Query result với danh sách vai trò của nhân viên
 */
export const useEmployeeRoles = (employeeId: number) => {
  return useQuery({
    queryKey: [ROLES_QUERY_KEY, 'employee', employeeId],
    queryFn: () => PermissionService.getEmployeeRoles(employeeId),
    select: data => data.result,
    enabled: !!employeeId,
  });
};

/**
 * Hook để tạo vai trò mới
 * @returns Mutation result cho việc tạo vai trò
 */
export const useCreateRole = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateRoleDto) => PermissionService.createRole(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [ROLES_QUERY_KEY] });
    },
  });
};

/**
 * Hook để cập nhật vai trò
 * @returns Mutation result cho việc cập nhật vai trò
 */
export const useUpdateRole = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateRoleDto }) =>
      PermissionService.updateRole(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [ROLES_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [ROLES_QUERY_KEY, variables.id] });
    },
  });
};

/**
 * Hook để xóa vai trò
 * @returns Mutation result cho việc xóa vai trò
 */
export const useDeleteRole = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => PermissionService.deleteRole(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [ROLES_QUERY_KEY] });
    },
  });
};

/**
 * Hook để cập nhật vai trò cho nhân viên
 * @returns Mutation result cho việc cập nhật vai trò
 */
export const useUpdateEmployeeRole = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ employeeId, data }: { employeeId: number; data: { roleIds: number[] } }) =>
      PermissionService.updateEmployeeRole(employeeId, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: [ROLES_QUERY_KEY, 'employee', variables.employeeId],
      });
      queryClient.invalidateQueries({
        queryKey: [EMPLOYEE_ROLES_QUERY_KEY, variables.employeeId],
      });
    },
  });
};

/**
 * Alias cho useUpdateEmployeeRole để gán vai trò cho nhân viên
 * @returns Mutation result cho việc gán vai trò
 */
export const useAssignRoleToEmployee = useUpdateEmployeeRole;
