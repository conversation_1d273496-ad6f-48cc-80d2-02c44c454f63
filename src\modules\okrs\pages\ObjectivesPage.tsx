import { format } from 'date-fns';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { ActiveFilters } from '@/modules/components/filters';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { apiClient } from '@/shared/api/axios';
import { Card, IconCard, ResponsiveGrid, Table, Tooltip } from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { TableColumn } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { useActiveFilters } from '@/shared/hooks/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import useSlideForm from '@/shared/hooks/useSlideForm';

import KeyResultForm from '../components/KeyResultForm';
import ObjectiveCard from '../components/ObjectiveCard';
import ObjectiveCreationFlow from '../components/ObjectiveCreationFlow';
import { useObjectives } from '../hooks/useObjectives';
import { KeyResultFormData } from '../types/key-result.types';
import {
  ObjectiveDto,
  ObjectiveQueryDto,
  ObjectiveStatus,
  ObjectiveType,
} from '../types/objective.types';

// Enum cho chế độ hiển thị
enum ViewMode {
  TABLE = 'table',
  CARD = 'card',
}

/**
 * Trang quản lý mục tiêu OKRs
 */
const ObjectivesPage: React.FC = () => {
  const { t } = useTranslation(['okrs', 'common']);

  // State cho chế độ hiển thị
  const [viewMode, setViewMode] = useState<ViewMode>(ViewMode.TABLE);

  // Ref cho infinite scroll
  const loadMoreRef = useRef<HTMLDivElement>(null);

  // Sử dụng hook animation cho form mục tiêu
  const { isVisible, showForm, hideForm } = useSlideForm();

  // Sử dụng hook animation cho form key result
  const {
    isVisible: isKeyResultFormVisible,
    showForm: showKeyResultForm,
    hideForm: hideKeyResultForm,
  } = useSlideForm();

  // State lưu trữ objective hiện tại để thêm key result
  const [currentObjective, setCurrentObjective] = useState<ObjectiveDto | null>(null);

  // Xử lý hiển thị form thêm key result
  const handleAddKeyResult = useCallback(
    (objective: ObjectiveDto) => {
      setCurrentObjective(objective);
      showKeyResultForm();
    },
    [showKeyResultForm]
  );

  // Xử lý submit form key result
  const handleKeyResultSubmit = async (data: KeyResultFormData) => {
    try {
      await apiClient.post('/api/okrs/key-results', data);
      hideKeyResultForm();
      // Refresh data
      refetch();
    } catch (error) {
      console.error('Lỗi khi thêm key result:', error);
    }
  };

  // Định nghĩa các tùy chọn bộ lọc
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common:all'), value: 'all' },
      { id: ObjectiveStatus.ACTIVE, label: t('common:active'), value: ObjectiveStatus.ACTIVE },
      {
        id: ObjectiveStatus.COMPLETED,
        label: t('common:completed'),
        value: ObjectiveStatus.COMPLETED,
      },
      {
        id: ObjectiveStatus.AT_RISK,
        label: t('common:atRisk', 'At Risk'),
        value: ObjectiveStatus.AT_RISK,
      },
      {
        id: ObjectiveStatus.BEHIND,
        label: t('common:behind', 'Behind'),
        value: ObjectiveStatus.BEHIND,
      },
    ],
    [t]
  );

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<ObjectiveDto>[]>(
    () => [
      { key: 'id', title: 'ID', dataIndex: 'id', width: '5%', sortable: true },
      {
        key: 'title',
        title: t('okrs:objective.table.title', 'Tiêu đề'),
        dataIndex: 'title',
        width: '20%',
        sortable: true,
      },
      {
        key: 'type',
        title: t('okrs:objective.table.type', 'Loại'),
        dataIndex: 'type',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          const type = value as ObjectiveType;
          let label = '';

          switch (type) {
            case ObjectiveType.COMPANY:
              label = t('okrs:objective.type.company', 'Công ty');
              break;
            case ObjectiveType.DEPARTMENT:
              label = t('okrs:objective.type.department', 'Phòng ban');
              break;
            case ObjectiveType.INDIVIDUAL:
              label = t('okrs:objective.type.individual', 'Cá nhân');
              break;
            default:
              label = type;
          }

          return <div>{label}</div>;
        },
      },
      {
        key: 'progress',
        title: t('okrs:objective.table.progress', 'Tiến độ'),
        dataIndex: 'progress',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          const progress = value as number;
          return (
            <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
              <div
                className="bg-blue-600 h-2.5 rounded-full dark:bg-blue-500"
                style={{ width: `${progress || 0}%` }}
              ></div>
              <div className="text-xs mt-1 text-center">{progress || 0}%</div>
            </div>
          );
        },
      },
      {
        key: 'status',
        title: t('okrs:objective.table.status', 'Trạng thái'),
        dataIndex: 'status',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          const status = value as string;
          let className = '';
          let label = '';

          switch (status) {
            case ObjectiveStatus.ACTIVE:
              className = 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
              label = t('common:active', 'Đang hoạt động');
              break;
            case ObjectiveStatus.COMPLETED:
              className = 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
              label = t('common:completed', 'Hoàn thành');
              break;
            case ObjectiveStatus.AT_RISK:
              className = 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
              label = t('common:atRisk', 'Có rủi ro');
              break;
            case ObjectiveStatus.BEHIND:
              className = 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
              label = t('common:behind', 'Chậm tiến độ');
              break;
            default:
              className = 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
              label = status || t('common:unknown', 'Không xác định');
          }

          return (
            <div className={`px-2 py-1 rounded-full text-center text-xs font-medium ${className}`}>
              {label}
            </div>
          );
        },
      },
      {
        key: 'startDate',
        title: t('okrs:objective.table.startDate', 'Ngày bắt đầu'),
        dataIndex: 'startDate',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          const date = value as string;
          return <div>{date ? format(new Date(date), 'dd/MM/yyyy') : ''}</div>;
        },
      },
      {
        key: 'endDate',
        title: t('okrs:objective.table.endDate', 'Ngày kết thúc'),
        dataIndex: 'endDate',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          const date = value as string;
          return <div>{date ? format(new Date(date), 'dd/MM/yyyy') : ''}</div>;
        },
      },
      {
        key: 'actions',
        title: t('common:actions', 'Thao tác'),
        width: '20%',
        render: (_: unknown, record: ObjectiveDto) => (
          <div className="flex space-x-2">
            <Tooltip content={t('okrs:keyResult.add', 'Thêm Key Result')} position="top">
              <IconCard
                icon="plus"
                variant="primary"
                size="sm"
                onClick={() => handleAddKeyResult(record)}
              />
            </Tooltip>
            <Tooltip content={t('common:view', 'Xem')} position="top">
              <IconCard
                icon="eye"
                variant="default"
                size="sm"
                onClick={() => console.log('View', record.id)}
              />
            </Tooltip>
            <Tooltip content={t('common:edit', 'Sửa')} position="top">
              <IconCard
                icon="edit"
                variant="default"
                size="sm"
                onClick={() => console.log('Edit', record.id)}
              />
            </Tooltip>
            <Tooltip content={t('common:delete', 'Xóa')} position="top">
              <IconCard
                icon="trash"
                variant="default"
                size="sm"
                onClick={() => console.log('Delete', record.id)}
              />
            </Tooltip>
          </div>
        ),
      },
    ],
    [t, handleAddKeyResult]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): ObjectiveQueryDto => {
    const queryParams: ObjectiveQueryDto = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
      startDate: params.dateRange[0] || undefined,
      endDate: params.dateRange[1] || undefined,
    };

    if (params.filterValue && params.filterValue !== 'all') {
      queryParams.status = params.filterValue as string;
    }

    if (params.dateRange[0] && params.dateRange[1]) {
      queryParams.startDate = params.dateRange[0];
      queryParams.endDate = params.dateRange[1];
    }

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<ObjectiveDto, ObjectiveQueryDto>({
      columns,
      filterOptions,
      showDateFilter: true,
      createQueryParams,
    })
  );

  // Gọi API lấy danh sách mục tiêu với queryParams từ dataTable
  const { data: objectivesData, isLoading, refetch } = useObjectives(dataTable.queryParams);

  // Xử lý load more cho infinite scroll
  const handleLoadMore = useCallback(() => {
    if (viewMode === ViewMode.CARD && objectivesData?.meta) {
      const { currentPage, totalPages } = objectivesData.meta;
      if (currentPage < totalPages) {
        dataTable.tableData.handlePageChange(currentPage + 1, dataTable.tableData.pageSize);
      }
    }
  }, [viewMode, objectivesData?.meta, dataTable.tableData]);

  // Intersection Observer cho infinite scroll
  useEffect(() => {
    if (viewMode !== ViewMode.CARD || !loadMoreRef.current) return;

    const observer = new IntersectionObserver(
      entries => {
        const target = entries[0];
        if (target.isIntersecting && !isLoading) {
          handleLoadMore();
        }
      },
      {
        root: null,
        rootMargin: '20px',
        threshold: 0.1,
      }
    );

    observer.observe(loadMoreRef.current);

    return () => {
      observer.disconnect();
    };
  }, [viewMode, isLoading, handleLoadMore]);

  // Xử lý thêm mới
  const handleAdd = () => {
    showForm();
  };

  // Xử lý hủy form
  const handleCancel = () => {
    hideForm();
  };

  // Xử lý chuyển đổi chế độ hiển thị
  const handleViewModeChange = (mode: ViewMode) => {
    setViewMode(mode);
  };

  // Tạo additional icons cho MenuIconBar
  const additionalIcons = useMemo(
    () => [
      {
        icon: 'list' as const,
        tooltip: t('common:table', 'Bảng'),
        variant: (viewMode === ViewMode.TABLE ? 'primary' : 'default') as
          | 'default'
          | 'primary'
          | 'secondary'
          | 'ghost',
        onClick: () => handleViewModeChange(ViewMode.TABLE),
      },
      {
        icon: 'grid' as const,
        tooltip: t('common:card', 'Thẻ'),
        variant: (viewMode === ViewMode.CARD ? 'primary' : 'default') as
          | 'default'
          | 'primary'
          | 'secondary'
          | 'ghost',
        onClick: () => handleViewModeChange(ViewMode.CARD),
      },
    ],
    [viewMode, t]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: dataTable.tableData.handleSortChange,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      [ObjectiveStatus.ACTIVE]: t('common:active', 'Đang hoạt động'),
      [ObjectiveStatus.COMPLETED]: t('common:completed', 'Hoàn thành'),
      [ObjectiveStatus.AT_RISK]: t('common:atRisk', 'Có rủi ro'),
      [ObjectiveStatus.BEHIND]: t('common:behind', 'Chậm tiến độ'),
    },
    t,
  });

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={true}
        showColumnFilter={true}
        additionalIcons={additionalIcons}
      />

      {/* Thêm component ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      <SlideInForm isVisible={isVisible}>
        <ObjectiveCreationFlow
          onSuccess={() => {
            hideForm();
            refetch();
          }}
          onCancel={handleCancel}
        />
      </SlideInForm>

      {/* Form thêm key result */}
      <SlideInForm isVisible={isKeyResultFormVisible}>
        <KeyResultForm
          objective={currentObjective}
          onSubmit={handleKeyResultSubmit}
          onCancel={hideKeyResultForm}
        />
      </SlideInForm>

      {/* Hiển thị dựa trên chế độ */}
      {viewMode === ViewMode.TABLE ? (
        <Card>
          <div className="w-full">
            <Table
              columns={dataTable.columnVisibility.visibleTableColumns}
              data={objectivesData?.items || []}
              rowKey="id"
              loading={isLoading}
              sortable={true}
              onSortChange={dataTable.tableData.handleSortChange}
              pagination={{
                current: objectivesData?.meta.currentPage || 1,
                pageSize: dataTable.tableData.pageSize,
                total: objectivesData?.meta.totalItems || 0,
                onChange: dataTable.tableData.handlePageChange,
                showSizeChanger: true,
                pageSizeOptions: [5, 10, 15, 20],
                showFirstLastButtons: true,
                showPageInfo: true,
              }}
            />
          </div>
        </Card>
      ) : (
        <div>
          <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 4 }}>
            {objectivesData?.items?.map(objective => (
              <ObjectiveCard
                key={objective.id}
                objective={objective}
                onAddKeyResult={() => handleAddKeyResult(objective)}
              />
            ))}
          </ResponsiveGrid>

          {/* Load more indicator cho infinite scroll */}
          {objectivesData?.meta &&
            objectivesData.meta.currentPage < objectivesData.meta.totalPages && (
              <div ref={loadMoreRef} className="flex justify-center py-4">
                {isLoading ? (
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-6 w-6 border-2 border-primary border-t-transparent"></div>
                    <span className="text-gray-500">{t('common:loading', 'Đang tải...')}</span>
                  </div>
                ) : (
                  <span className="text-gray-500">{t('common:loadMore', 'Cuộn để tải thêm')}</span>
                )}
              </div>
            )}
        </div>
      )}
    </div>
  );
};

export default ObjectivesPage;
