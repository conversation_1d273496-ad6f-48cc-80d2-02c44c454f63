import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ArrowLeft } from 'lucide-react';

import { Button, Card } from '@/shared/components/common';
import { ObjectiveType } from '../types/objective.types';

import CompanyObjectiveForm from './CompanyObjectiveForm';
import DepartmentObjectiveForm from './DepartmentObjectiveForm';
import ObjectiveForm from './ObjectiveForm';
import ObjectiveTypeSelection from './ObjectiveTypeSelection';

export interface ObjectiveCreationFlowProps {
  /**
   * Callback khi form được submit thành công
   */
  onSuccess?: () => void;

  /**
   * Callback khi form bị đóng
   */
  onCancel?: () => void;

  /**
   * ID của chu kỳ OKR mặc định (nếu có)
   */
  defaultCycleId?: string;
}

/**
 * Component quản lý quy trình tạo mục tiêu 2 bước
 */
const ObjectiveCreationFlow: React.FC<ObjectiveCreationFlowProps> = ({
  onSuccess,
  onCancel,
  defaultCycleId,
}) => {
  const { t } = useTranslation(['okrs', 'common']);

  // State quản lý bước hiện tại và loại mục tiêu đã chọn
  const [currentStep, setCurrentStep] = useState<'selection' | 'form'>('selection');
  const [selectedType, setSelectedType] = useState<ObjectiveType | null>(null);

  // Xử lý khi chọn loại mục tiêu
  const handleSelectType = (type: ObjectiveType) => {
    setSelectedType(type);
    setCurrentStep('form');
  };

  // Xử lý quay lại bước chọn loại
  const handleBackToSelection = () => {
    setCurrentStep('selection');
    setSelectedType(null);
  };

  // Xử lý khi form được submit thành công
  const handleFormSuccess = () => {
    // Reset state
    setCurrentStep('selection');
    setSelectedType(null);

    // Gọi callback onSuccess
    if (onSuccess) {
      onSuccess();
    }
  };

  // Xử lý khi hủy form
  const handleFormCancel = () => {
    // Reset state
    setCurrentStep('selection');
    setSelectedType(null);

    // Gọi callback onCancel
    if (onCancel) {
      onCancel();
    }
  };

  // Render form tương ứng với loại mục tiêu đã chọn
  const renderForm = () => {
    switch (selectedType) {
      case ObjectiveType.INDIVIDUAL:
        return (
          <ObjectiveForm
            onSuccess={handleFormSuccess}
            onCancel={handleFormCancel}
            defaultCycleId={defaultCycleId}
          />
        );

      case ObjectiveType.DEPARTMENT:
        return (
          <DepartmentObjectiveForm
            onSuccess={handleFormSuccess}
            onCancel={handleFormCancel}
            defaultCycleId={defaultCycleId}
          />
        );

      case ObjectiveType.COMPANY:
        return (
          <CompanyObjectiveForm
            onSuccess={handleFormSuccess}
            onCancel={handleFormCancel}
            defaultCycleId={defaultCycleId}
          />
        );

      default:
        return null;
    }
  };

  // Render bước chọn loại mục tiêu
  if (currentStep === 'selection') {
    return (
      <Card className="mx-auto">
        <ObjectiveTypeSelection onSelectType={handleSelectType} />
        
        {/* Nút hủy */}
        <div className="flex justify-end mt-6 pt-4 border-t border-border">
          <Button variant="outline" onClick={onCancel}>
            {t('common:cancel', 'Hủy')}
          </Button>
        </div>
      </Card>
    );
  }

  // Render bước form tạo mục tiêu
  return (
    <div className="space-y-4">
      {/* Nút quay lại */}
      <div className="flex items-center">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleBackToSelection}
          className="flex items-center gap-2 text-muted-foreground hover:text-foreground"
        >
          <ArrowLeft className="h-4 w-4" />
          {t('okrs:objective.form.backToSelection', 'Quay lại chọn loại mục tiêu')}
        </Button>
      </div>

      {/* Form tương ứng */}
      {renderForm()}
    </div>
  );
};

export default ObjectiveCreationFlow;
